'use client';
import { useState, useEffect, useCallback } from 'react';
import { useParams } from 'next/navigation';
import { useAuth } from '../../../contexts/AuthContext';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '../../../lib/firebase';
import { getCurrentMatchday } from '../../../utils/dateUtils';

export default function UserPredictionsPage() {
  const { id } = useParams();
  const { user } = useAuth();
  const [userName, setUserName] = useState('');
  const [currentUserName, setCurrentUserName] = useState('');
  const [userPredictions, setUserPredictions] = useState(null);
  const [currentUserPredictions, setCurrentUserPredictions] = useState(null);
  const [matches, setMatches] = useState([]);
  const [loading, setLoading] = useState(true);
  const currentMatchday = getCurrentMatchday();

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);

      const userId = Array.isArray(id) ? id[0] : id;
      console.log("Fetching data for user ID:", userId);
      console.log("Current matchday:", currentMatchday);

      // Fetch user info
      const userDoc = await getDoc(doc(db, "users", userId));
      if (userDoc.exists()) {
        const userData = userDoc.data();
        console.log("User data found:", userData);
        setUserName(userData.name);
      } else {
        console.log("User document not found for ID:", userId);
        setUserName("Unknown User");
      }

      // Fetch current user info
      if (user) {
        const currentUserDoc = await getDoc(doc(db, "users", user.uid));
        if (currentUserDoc.exists()) {
          const currentUserData = currentUserDoc.data();
          console.log("Current user data found:", currentUserData);
          setCurrentUserName(currentUserData.name);
        } else {
          console.log("Current user document not found for ID:", user.uid);
        }
      }

      // Fetch selected matches
      const selectedMatchesResponse = await fetch('/api/matches/selected');
      if (!selectedMatchesResponse.ok) {
        throw new Error(`Failed to fetch selected matches: ${selectedMatchesResponse.status}`);
      }
      const selectedMatchesData = await selectedMatchesResponse.json();
      console.log("Selected matches data:", selectedMatchesData);

      // Check if selected matches exist
      if (!selectedMatchesData || !selectedMatchesData.matches) {
        console.log("No selected matches found for current matchday");
        setMatches([]);
        setLoading(false);
        return;
      }

      // Fetch match details
      const matchesResponse = await fetch('/api/matches');
      if (!matchesResponse.ok) {
        console.error(`Failed to fetch matches: ${matchesResponse.status}`);
        // Continue with empty matches if API fails
        setMatches([]);
        setLoading(false);
        return;
      }
      const matchesData = await matchesResponse.json();
      console.log("Matches data:", matchesData);

      // Create match mapping
      const matchMapping = matchesData.filter((match: any) =>
        selectedMatchesData.matches.regular.includes(match.id) ||
        selectedMatchesData.matches.special === match.id
      ).map((match: any) => ({
        id: match.id,
        homeTeam: match.homeTeam.name,
        awayTeam: match.awayTeam.name,
        isSpecial: selectedMatchesData.matches.special === match.id
      }));

      console.log("Match mapping:", matchMapping);
      setMatches(matchMapping);

      // Fetch user predictions
      const userPredictionsResponse = await fetch(`/api/predictions/user/${userId}`);
      if (!userPredictionsResponse.ok) {
        console.error(`Failed to fetch user predictions: ${userPredictionsResponse.status}`);
      }
      const userPredictionsData = await userPredictionsResponse.json();
      console.log("User predictions data:", userPredictionsData);
      setUserPredictions(userPredictionsData);

      // Fetch current user predictions
      if (user) {
        const currentUserPredictionsResponse = await fetch('/api/predictions', {
          headers: { "x-user": user.uid }
        });
        if (!currentUserPredictionsResponse.ok) {
          console.error(`Failed to fetch current user predictions: ${currentUserPredictionsResponse.status}`);
        }
        const currentUserPredictionsData = await currentUserPredictionsResponse.json();
        console.log("Current user predictions data:", currentUserPredictionsData);
        setCurrentUserPredictions(currentUserPredictionsData);
      }

      setLoading(false);
    } catch (error) {
      console.error("Error fetching data:", error);
      setLoading(false);
    }
  }, [user, id, currentMatchday]);

  useEffect(() => {
    if (user && id) {
      fetchData();
    }
  }, [user, id, fetchData]);

  if (loading) {
    return <div className="container mx-auto p-4 text-center text-gray-900 dark:text-slate-100">Loading...</div>;
  }

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6 text-center text-gray-900 dark:text-slate-100">
        {userName || "Loading..."}&apos;s Predictions - Matchday {currentMatchday}
      </h1>

      {/* Debug information */}
      {process.env.NODE_ENV === 'development' && (
        <div className="bg-gray-100 dark:bg-slate-700 p-4 mb-4 rounded text-sm text-gray-900 dark:text-slate-100">
          <p><strong>Debug Info:</strong></p>
          <p>User ID: {Array.isArray(id) ? id[0] : id}</p>
          <p>User Name: {userName || "Not found"}</p>
          <p>Current User Name: {currentUserName || "Not found"}</p>
          <p>Matches Count: {matches.length}</p>
          <p>User Predictions: {userPredictions ? "Found" : "Not found"}</p>
          <p>Current User Predictions: {currentUserPredictions ? "Found" : "Not found"}</p>
        </div>
      )}

      <div className="bg-white dark:bg-slate-800 rounded-lg shadow-md p-6 mb-8">
        <h2 className="text-xl font-semibold mb-4 text-center text-gray-900 dark:text-slate-100">Prediction Comparison</h2>

        {matches.length === 0 ? (
          <div className="text-center py-8 text-gray-500 dark:text-slate-400">
            <p className="text-lg mb-2">No matches have been selected for Matchday {currentMatchday} yet.</p>
            <p className="text-sm">Please check back later or contact an administrator to set up matches for this week.</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full bg-white dark:bg-slate-800">
              <thead>
                <tr className="bg-gray-100 dark:bg-slate-700 border-b border-gray-200 dark:border-slate-600">
                  <th className="py-3 px-4 text-left text-gray-900 dark:text-slate-100">Match</th>
                  <th className="py-3 px-4 text-center text-gray-900 dark:text-slate-100">{userName}&apos;s Pick</th>
                  <th className="py-3 px-4 text-center text-gray-900 dark:text-slate-100">{currentUserName}&apos;s Pick</th>
                </tr>
              </thead>
              <tbody>
                {matches.map(match => {
                  const userPick = userPredictions?.predictions?.[match.id];
                  const currentUserPick = currentUserPredictions?.predictions?.[match.id];

                  const getUserPickDisplay = (pick: any) => {
                    if (!pick) return 'No prediction';
                    return pick === 'HOME_WIN' ? match.homeTeam :
                           pick === 'AWAY_WIN' ? match.awayTeam : 'Draw';
                  };

                  const userPickDisplay = getUserPickDisplay(userPick);
                  const currentUserPickDisplay = getUserPickDisplay(currentUserPick);
                  const sameChoice = userPick && currentUserPick && userPick === currentUserPick;

                  return (
                    <tr key={match.id} className="border-b border-gray-200 dark:border-slate-600 hover:bg-gray-50 dark:hover:bg-slate-700">
                      <td className="py-3 px-4 text-gray-900 dark:text-slate-100">
                        <div className="flex flex-col">
                          <span className="font-medium">{match.homeTeam} vs {match.awayTeam}</span>
                          {match.isSpecial && <span className="text-xs text-purple-600 dark:text-purple-400 font-semibold">Special Match</span>}
                        </div>
                      </td>
                      <td className="py-3 px-4 text-center text-gray-900 dark:text-slate-100">{userPickDisplay}</td>
                      <td className={`py-3 px-4 text-center text-gray-900 dark:text-slate-100 ${sameChoice ? 'bg-green-100 dark:bg-green-800' : ''}`}>
                        {currentUserPickDisplay}
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}
